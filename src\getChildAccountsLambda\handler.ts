import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { verifyToken } from '../shared/utils/tokenUtils';
import { SuccessResponse, ErrorResponse } from '../shared/responseUtils';
import { getChildAccountsByParent, incrementChildAccountsListRetrievalCount } from '../shared/database/userOperations';
import { parseDateInput } from '../shared/utils/dateUtils';
import { getConfig } from '../shared/services/configService';

export const getChildAccountsLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Load configuration from the service
  const config = await getConfig(event);
  
  console.log('[GET-CHILD-ACCOUNTS] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] GetChildAccountsLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    userTable: config.USER_DETAILS_TABLE_NAME
  });

  try {
    // 1. Token verification
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Missing authorization token');
    }

    
    const token = authHeader.replace('Bearer ', '');
    const decodedToken = await verifyToken(token);
    if (!decodedToken || !decodedToken.accountType || decodedToken.accountType !== 'parent' || !decodedToken.publicKey) {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Only parent accounts can access this data');
    }

    // Try to increment counter but don't fail if it errors
    try {
      await incrementChildAccountsListRetrievalCount(decodedToken.userID, config.USER_DETAILS_TABLE_NAME);
    } catch (counterError) {
      console.warn('Failed to increment counter:', counterError);
      // Continue execution even if counter fails
    }

        
        // 2. Get query parameters
        const { 
            search,
            startDate,
            endDate,
            limit = '50',
            lastKey
        } = event.queryStringParameters || {};

        // Validate date parameters if provided
        let startTimestamp: number | undefined;
        let endTimestamp: number | undefined;

        if (startDate) {
            try {
                startTimestamp = parseDateInput(startDate);
            } catch (error) {
                return ErrorResponse(400, 'INVALID_DATE_FORMAT', 
                    'Start date must be either a timestamp or a valid date format (YYYY-MM-DD or ISO 8601)');
            }
        }

        if (endDate) {
            try {
                endTimestamp = parseDateInput(endDate);
            } catch (error) {
                return ErrorResponse(400, 'INVALID_DATE_FORMAT', 
                    'End date must be either a timestamp or a valid date format (YYYY-MM-DD or ISO 8601)');
            }
        }

        // Validate limit
        const parsedLimit = parseInt(limit);
        if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > 100) {
            return ErrorResponse(400, 'INVALID_LIMIT', 'Limit must be between 1 and 100');
        }

        // Parse lastKey if provided
        let lastEvaluatedKey;
        if (lastKey) {
            try {
                lastEvaluatedKey = JSON.parse(Buffer.from(lastKey, 'base64').toString());
            } catch {
                return ErrorResponse(400, 'INVALID_PAGINATION_TOKEN', 'Invalid pagination token');
            }
        }

    // Get accounts and stats in a single query
    const { 
      accounts, 
      lastEvaluatedKey: newLastKey,
      totalAccounts,
      activeAccounts
    } = await getChildAccountsByParent(
      decodedToken.publicKey,
      search,
      startTimestamp,
      endTimestamp,
      parsedLimit,
      lastEvaluatedKey,
      config.USER_DETAILS_TABLE_NAME
    );

        // Format response
        const formattedAccounts = accounts.map(account => ({
            username: account.username,
            email: account.email,
            lastSeen: account.lastSeen,
            registered: account.registered
        }));

        // Prepare response
        const response = {
            data: {
                stats: {
                    total: totalAccounts,
                    active: activeAccounts
                },
                accounts: formattedAccounts,
                pagination: {
                    limit: parsedLimit,
                    hasMore: !!newLastKey,
                    ...(newLastKey && {
                        nextPageToken: Buffer.from(JSON.stringify(newLastKey)).toString('base64')
                    })
                }
            }
        };

        return SuccessResponse(200, response);
    } catch (error) {
        console.error('Error in getChildAccountsLambda:', error);
        return ErrorResponse(500, 'INTERNAL_ERROR', 'An error occurred');
    }
};
     
 
