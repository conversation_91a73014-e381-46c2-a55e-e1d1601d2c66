import { DynamoDB } from 'aws-sdk';
import { calculateCostsForUser } from '../shared/utils/costCalculator';
import { storeMonthlyBillingRecord } from '../shared/database/billingOperations';
import { checkAndResetMonthlyOperations, resetAllChildAccountCounters } from '../shared/database/userOperations';
import { loadParametersFromEnv } from '../shared/utils/parameterLoader';

const dynamoDB = new DynamoDB.DocumentClient();
const BATCH_SIZE = 10; // Process 10 accounts at a time
const MAX_RETRIES = 3; // Maximum number of retries for failed accounts


/**
 * Process a single parent account - calculate costs, store billing record, update balance
 */
async function processAccount(account: any, config: Record<string, any>, retryCount = 0): Promise<boolean> {
    try {
        console.log(`Processing account: ${account.publicKey} (Attempt ${retryCount + 1})`);
        
        // cost calculation and storage logic
        const costs = await calculateCostsForUser(account.publicKey);

        const currentMonth = new Date().toISOString().slice(0, 7);
        const billingRecord = {
            publicKey: account.publicKey,
            monthYear: currentMonth,
            totalAccounts: costs.totalAccounts,
            costAssociatedWithAccounts: Number(costs.accountsCost.toFixed(2)),
            totalIOInteractions: costs.totalOperations,
            costAssociatedWithIO: Number(costs.ioCost.toFixed(2)),
            totalFinalCost: Number(costs.totalCost.toFixed(2)),
            timestamp: Date.now()
        };
        
        await storeMonthlyBillingRecord(billingRecord, config.BILLING_TABLE_NAME);
        await checkAndResetMonthlyOperations(account.userID, config.USER_DETAILS_TABLE_NAME);
        
        console.log(`Retrieved account balances for ${account.userID}:`, {
            accountBalance: account.accountBalance,
            availableBalance: account.availableBalance,
            accountType: account.accountType
        });

        // Calculate the new available balance by subtracting the billing amount
        const newAvailableBalance = Number((account.availableBalance - billingRecord.totalFinalCost).toFixed(2));
        
        // Use the new available balance as the new account balance
        const newAccountBalance = newAvailableBalance;
        
        // Update both balances
        const params = {
            TableName: config.USER_DETAILS_TABLE_NAME,
            Key: { userID: account.userID },
            UpdateExpression: 'SET accountBalance = :newBalance, availableBalance = :newAvailBalance',
            ExpressionAttributeValues: {
                ':newBalance': newAccountBalance,
                ':newAvailBalance': newAvailableBalance
            }
        };

        await dynamoDB.update(params).promise();

        // Log successful balance update
        console.log(`Account balances updated successfully for ${account.userID}`);
        console.log(`Successfully processed billing for ${account.publicKey}`);
        
        return true;
    } catch (error) {
        // If we haven't exceeded max retries, try again with exponential backoff
        if (retryCount < MAX_RETRIES) {
            const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
            console.log(`Retrying account ${account.publicKey} after ${delay}ms (Attempt ${retryCount + 1} failed)`);
            await new Promise(resolve => setTimeout(resolve, delay));
            return processAccount(account, config, retryCount + 1);
        }
        
        // detailed error information for debugging
        console.error(`Failed to process billing for ${account.publicKey} after ${MAX_RETRIES + 1} attempts:`, error);
        console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
        return false;
    }
}

/**
 * Process a batch of accounts in parallel
 */
async function processBatch(accounts: any[], config: Record<string, any>): Promise<{ successes: number; failures: number }> {
    const results = await Promise.all(
        accounts.map(account => processAccount(account, config))
    );
    
    const successes = results.filter(result => result).length;
    const failures = results.filter(result => !result).length;
    
    return { successes, failures };
}

/**
 * Get all parent accounts with pagination
 */
async function getAllParentAccounts(tableName: string): Promise<any[]> {
    let allAccounts: any[] = [];
    let lastEvaluatedKey: any = undefined;
    
    do {
        const params: DynamoDB.DocumentClient.ScanInput = {
            TableName: tableName,
            FilterExpression: 'accountType = :accountType',
            ExpressionAttributeValues: {
                ':accountType': 'parent'
            },
            Limit: 100 // Fetch 100 items per scan
        };
        
        // Add ExclusiveStartKey for pagination if we have a lastEvaluatedKey
        if (lastEvaluatedKey) {
            params.ExclusiveStartKey = lastEvaluatedKey;
        }
        
        console.log('Scanning parent accounts with params:', JSON.stringify(params, null, 2));
        const result = await dynamoDB.scan(params).promise();
        
        // Add the accounts from this page to our collection
        if (result.Items && result.Items.length > 0) {
            allAccounts = [...allAccounts, ...result.Items];
        }
        
        // Update lastEvaluatedKey for the next iteration
        lastEvaluatedKey = result.LastEvaluatedKey;
        
        console.log(`Retrieved ${result.Items?.length || 0} accounts, total so far: ${allAccounts.length}`);
    } while (lastEvaluatedKey); // Continue until we've scanned all items
    
    return allAccounts;
}

export const monthlyBillingStorageLambda = async (): Promise<void> => {
    try {
        const environment = process.env.ENVIRONMENT || 'live';
        console.log(`Starting monthly billing storage process for ${environment} environment`);
        
        // Load configuration for the environment
        const config = await loadParametersFromEnv();
        
        console.log('[CONFIG] MonthlyBillingStorageLambda using configuration', {
            environment,
            userTable: config.USER_DETAILS_TABLE_NAME,
            billingTable: config.BILLING_TABLE_NAME
        });
        
        // Get all parent accounts with pagination
        const parentAccounts = await getAllParentAccounts(config.USER_DETAILS_TABLE_NAME);
        
        console.log(`Found ${parentAccounts.length} parent accounts to process`);
        console.log('Parent accounts:', JSON.stringify(parentAccounts.map(acc => ({
            publicKey: acc.publicKey,
            userID: acc.userID,
            accountType: acc.accountType
        })), null, 2));

        let totalSuccesses = 0;
        let totalFailures = 0;

        // Process accounts in batches
        for (let i = 0; i < parentAccounts.length; i += BATCH_SIZE) {
            const batch = parentAccounts.slice(i, i + BATCH_SIZE);
            console.log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(parentAccounts.length / BATCH_SIZE)}`);
            
            const { successes, failures } = await processBatch(batch, config);
            totalSuccesses += successes;
            totalFailures += failures;
            
            console.log(`Batch completed. Successes: ${successes}, Failures: ${failures}`);
        }

        console.log(`Billing process completed. Total Successes: ${totalSuccesses}, Total Failures: ${totalFailures}`);
        
        if (totalFailures > 0) {
            console.log(`Warning: ${totalFailures} accounts failed processing after all retries`);
        }

        console.log('Starting child account counter reset');
        await resetAllChildAccountCounters(config.USER_DETAILS_TABLE_NAME);
        console.log('Completed child account counter reset');
    } catch (error) {
        console.error('Monthly billing storage failed:', error);
        console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
        throw error;
    }
}