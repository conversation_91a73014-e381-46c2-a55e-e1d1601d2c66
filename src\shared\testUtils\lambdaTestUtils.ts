import { APIGatewayProxyEvent, Context, APIGatewayProxyResult } from 'aws-lambda';
import { clearTestDatabase, createTestTable } from './databaseTestUtils';  // Since they're in the same folder
import { hashPassword } from '../utils/passwordUtils';
import { generatePublicKey } from '../utils/apiKeyUtils';
import { User } from '../types';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

// Standard mock context used across all lambda tests
export const createMockContext = (): Partial<Context> => ({
  awsRequestId: 'test-request-id',
  functionName: 'test-function',
  functionVersion: 'test-version',
  invokedFunctionArn: 'test-arn',
  memoryLimitInMB: '128',
  callbackWaitsForEmptyEventLoop: true,
  logGroupName: 'test-log-group',
  logStreamName: 'test-log-stream',
  getRemainingTimeInMillis: () => 1000
});

// Add other utility functions but remove DynamoDB mocking code

export const setupTestEnvironment = async () => {
  try {
    await createTestTable();
    await clearTestDatabase();
  } catch (error) {
    console.error('Error setting up test environment:', error);
    throw error;
  }
};

export const VALID_TEST_PASSWORD = 'Test@Password123';

export const createTestUser = async (props: Partial<User> = {}): Promise<User> => {
  const timestamp = Date.now();
  const hashedPassword = await hashPassword(VALID_TEST_PASSWORD);
  const defaultUser: User = {
    userID: uuidv4(),
    username: `testuser_${timestamp}`,
    email: `test${timestamp}@example.com`,
    password: hashedPassword,
    emailVerified: false,
    accountStatus: 'active',
    createdAt: timestamp,
    verificationToken: 'ABC123XYZ789',
    verificationTokenExpiry: timestamp + 3600000,
    lastVerificationTokenSentAt: timestamp,
    publicKey: `APK_${crypto.randomBytes(16).toString('hex')}_${timestamp}`,
    loginAttempts: 0,
    lastLoginAttempt: 0,
    lockedUntil: undefined,
    resetPasswordOTP: undefined,
    resetPasswordOTPExpiry: 0,
    lastResetPasswordRequestAt: 0,
    parentAccount: 'ROOT',
    organizationUpdateCount: 0,
    organizationName: undefined,
    organizationUrl: undefined,
    accountType: 'child', // or 'parent' based on the context
    emailConfirmationCount: 0,
    resendEmailCount: 0,
    resetPasswordRequestCount: 0,
    passwordUpdateCount: 0,
    signInCount: 0,
    lastLowBalanceNotificationAt: 0,
    lastCriticalBalanceNotificationAt: 0,
    lastDepletedBalanceNotificationAt: 0
  };

  return {
    ...defaultUser,
    ...props
  };
};

export const expectSuccessResponse = (response: APIGatewayProxyResult, statusCode: number, data: any) => {
  expect(response.statusCode).toBe(statusCode);
  const body = JSON.parse(response.body);
  expect(body.success).toBe(true);
  expect(body.data).toMatchObject(data);
};

export const expectErrorResponse = (response: APIGatewayProxyResult, statusCode: number, code: string, message: string) => {
  expect(response.statusCode).toBe(statusCode);
  const body = JSON.parse(response.body);
  expect(body.success).toBe(false);
  expect(body.error.code).toBe(code);
  expect(body.error.message).toBe(message);
};

export const createLambdaEvent = (req: any): Partial<APIGatewayProxyEvent> => ({
  body: req.body,
  headers: req.headers,
  httpMethod: req.method,
  path: req.path,
  isBase64Encoded: false,
  requestContext: {
    requestId: 'test-request-id',
    requestTimeEpoch: Date.now()
  } as any,
});

export const createMockEvent = (body: any, path: string): Partial<APIGatewayProxyEvent> => ({
  body: JSON.stringify(body),
  headers: {
    'Content-Type': 'application/json'
  },
  httpMethod: 'POST',
  path,
  requestContext: {
    requestId: 'test-request-id',
    requestTimeEpoch: Date.now()
  } as any,
  isBase64Encoded: false
});

