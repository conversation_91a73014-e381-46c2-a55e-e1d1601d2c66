import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { initializePayment } from '../shared/services/balanceService';
import { getUserById } from '../shared/database/userOperations';
import { PaymentUser } from '../shared/services/balanceService';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { verifyToken } from '../shared/utils/tokenUtils';
import { getConfig } from '../shared/services/configService';

export async function paymentInitializationLambda(
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> {
  try {
    // Load configuration from the service
    const config = await getConfig(event);
    
    console.log('[PAYMENT-INIT] Lambda triggered', {
      path: event.path,
      method: event.httpMethod,
      hasBody: !!event.body,
      environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
      userTable: config.USER_DETAILS_TABLE_NAME,
      paymentTable: config.PAYMENT_HISTORY_TABLE_NAME
    });

    // Add debug log for raw event body
    console.log('Raw event body before parsing:', event.body);
    console.log('Raw event body type:', typeof event.body);

    // 1. Auth Check
    const authHeader = event.headers.Authorization || event.headers.authorization;
    console.log('Checking authorization header:', { hasAuth: !!authHeader });
    if (!authHeader) {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Missing authorization token');
    }

    // 2. Verify Token
    const token = authHeader.replace('Bearer ', '');
    const decodedToken = await verifyToken(token);
    console.log('Token verification result:', { 
      userId: decodedToken?.userID,
      accountType: decodedToken?.accountType 
    });
    if (!decodedToken || !decodedToken.accountType || decodedToken.accountType !== 'parent') {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Only parent accounts can make payments');
    }

    // 3. Parse and Validate Request Body
    let parsedBody;
    try {
      parsedBody = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
      console.log('First parse result:', parsedBody);
      
      // If the body is still a string (double stringified), parse again
      if (typeof parsedBody === 'string') {
        parsedBody = JSON.parse(parsedBody);
        console.log('Second parse result:', parsedBody);
      }
    } catch (e) {
      console.error('Error parsing body:', e);
      return ErrorResponse(400, 'INVALID_BODY', 'Could not parse request body');
    }

    const { amount, invoiceEmail } = parsedBody;
    console.log('Final parsed body:', { amount, invoiceEmail, bodyType: typeof amount });

    if (!amount || typeof amount !== 'number' || amount <= 0 || amount > 999999) {
      return ErrorResponse(400, 'INVALID_AMOUNT', 'Amount must be between $0.01 and $999,999');
    }

    // 4. Get User Details with table name from config
    const user = await getUserById(decodedToken.userID, config.USER_DETAILS_TABLE_NAME);
    console.log('Retrieved user details:', { 
      userId: user?.userID,
      hasBalance: user?.accountBalance !== undefined,
      balance: user?.accountBalance
    });
    if (!user) {
      return ErrorResponse(404, 'USER_NOT_FOUND', 'User not found');
    }
''
    // 5. Format User Data for Payment
    const paymentUser: PaymentUser = {
      userID: user.userID,
      publicKey: user.publicKey,
      email: user.email,
      username: user.username,
      balance: user.accountBalance || 0,
      accountType: user.accountType
    };

    // 6. Initialize Payment - pass table name from config
    console.log('About to initialize payment - checking Secrets Manager access...', {
      userId: paymentUser.userID,
      amount,
      currentBalance: paymentUser.balance
    });
    const result = await initializePayment(
      paymentUser, 
      amount, 
      invoiceEmail, 
      config.PAYMENT_HISTORY_TABLE_NAME,
      config.USER_DETAILS_TABLE_NAME // Pass user table name as well
    );


    // 7. Return Success Response
    console.log('Payment initialization successful:', {
      paymentId: result.paymentId,
      hasClientSecret: !!result.clientSecret
    });
    return SuccessResponse(200, result);
  } catch (error) {
    console.error('Payment initialization error:', error);
    return ErrorResponse(
      500, 
      'PAYMENT_INIT_ERROR',
      error instanceof Error ? error.message : 'Error initializing payment'
    );
  }
}
 

 
