import { CreateTableCommandInput } from "@aws-sdk/client-dynamodb";

export const USER_TABLE_CONFIG: CreateTableCommandInput = {
  TableName: process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication',
  BillingMode: 'PAY_PER_REQUEST',
  KeySchema: [
    { AttributeName: 'userID', KeyType: 'HASH' }
  ],
  AttributeDefinitions: [
    { AttributeName: 'userID', AttributeType: 'S' },
    { AttributeName: 'email', AttributeType: 'S' },
    { AttributeName: 'username', AttributeType: 'S' },
    { AttributeName: 'accountStatus', AttributeType: 'S' },
    { AttributeName: 'OTP', AttributeType: 'S' },
    { AttributeName: 'createdAt', AttributeType: 'N' },
    { AttributeName: 'loginAttempts', AttributeType: 'N' },
    { AttributeName: 'lastLoginAttempt', AttributeType: 'N' },
    { AttributeName: 'lockedUntil', AttributeType: 'N' },
    { AttributeName: 'publicKey', AttributeType: 'S' },
    { AttributeName: 'resetPasswordOTP', AttributeType: 'S' },
    { AttributeName: 'resetPasswordOTPExpiry', AttributeType: 'N' },
    { AttributeName: 'lastResetPasswordRequestAt', AttributeType: 'N' },
    { AttributeName: 'parentAccount', AttributeType: 'S' },
    { AttributeName: 'emailUsernameCombo', AttributeType: 'S' }
  ],
  GlobalSecondaryIndexes: [
    {
      IndexName: 'emailIndex',
      KeySchema: [
        { AttributeName: 'email', KeyType: 'HASH' }
      ],
      Projection: { ProjectionType: 'ALL' }
    },
    {
      IndexName: 'usernameIndex',
      KeySchema: [
        { AttributeName: 'username', KeyType: 'HASH' }
      ],
      Projection: { ProjectionType: 'ALL' }
     
    },
    {
      IndexName: 'emailParentIndex',
      KeySchema: [
        { AttributeName: 'email', KeyType: 'HASH' },
        { AttributeName: 'parentAccount', KeyType: 'RANGE' }
      ],
      Projection: { ProjectionType: 'ALL' },

    },
    {
      IndexName: 'otpIndex',
      KeySchema: [
        { AttributeName: 'OTP', KeyType: 'HASH' }
      ],
      Projection: { ProjectionType: 'ALL' }
    },
    {
      IndexName: 'accountStatusIndex',
      KeySchema: [
        { AttributeName: 'accountStatus', KeyType: 'HASH' },
        { AttributeName: 'createdAt', KeyType: 'RANGE' }
      ],
      Projection: { ProjectionType: 'ALL' }
    },
    {
      IndexName: 'publicKeyIndex',
      KeySchema: [
        { AttributeName: 'publicKey', KeyType: 'HASH' }
      ],
      Projection: { ProjectionType: 'ALL' }
    },
    {
      IndexName: 'parentAccountIndex',
      KeySchema: [
        { AttributeName: 'parentAccount', KeyType: 'HASH' },
        { AttributeName: 'createdAt', KeyType: 'RANGE' }
      ],
      Projection: { ProjectionType: 'ALL' }
    },
    {
      IndexName: 'parentAccountComboIndex',
      KeySchema: [
        { AttributeName: 'parentAccount', KeyType: 'HASH' },
        { AttributeName: 'emailUsernameCombo', KeyType: 'RANGE' }
      ],
      Projection: { ProjectionType: 'ALL' }
    }
  ]
}; 

