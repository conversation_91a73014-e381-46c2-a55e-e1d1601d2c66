{"name": "lambda-scaffolding", "version": "1.0.0", "main": "index.js", "scripts": {"test": "jest", "test:coverage": "jest --coverage --forceExit", "build": "tsc", "package": "npm run build && bash package_lambdas.sh", "make:badges": "jest-coverage-badges --output ./badges", "dev": "ts-node src/server.ts", "dev:watch": "ts-node-dev --respawn src/server.ts", "start": "node dist/server.js", "test:cors": "jest src/getOrganizationDetailsLambda/tests/cors.test.ts --verbose"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/aws-lambda": "^8.10.145", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/html-escaper": "^3.0.2", "@types/jest": "^29.5.13", "@types/jsonwebtoken": "^9.0.7", "@types/uuid": "^10.0.0", "@types/yup": "^0.29.14", "jest": "^29.7.0", "jest-coverage-badges": "^1.1.2", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.6.3"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.679.0", "@aws-sdk/lib-dynamodb": "^3.679.0", "@aws-sdk/util-dynamodb": "^3.485.0", "@sendgrid/mail": "^8.1.4", "@types/aws-sdk": "^0.0.42", "@types/nodemailer": "^6.4.16", "@types/pdfkit": "^0.13.9", "@types/stripe": "^8.0.416", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1692.0", "axios": "^1.8.3", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "html-escaper": "^3.0.3", "jsonwebtoken": "^9.0.2", "nanoid": "^5.0.8", "nodemailer": "^6.9.16", "pdfkit": "^0.16.0", "stripe": "^17.7.0", "yup": "^1.4.0"}}