import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { promoteAccountLambda } from '../handler';
import { 
  createMockEvent, 
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse,
  VALID_TEST_PASSWORD
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, initializeTestDatabase, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { generateToken } from '../../shared/utils/tokenUtils';

// Mock any external services if needed
// jest.mock('../../shared/services/configService');

describe('Promote Account Lambda Handler', () => {
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    process.env.JWT_SECRET = 'test-jwt-secret';
    process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
    await initializeTestDatabase();
  });

 

 

  it('should successfully promote a child account to parent', async () => {
    // Create a test child user
    const childUser = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: 'APK_authiqa_parent_key',
      accountType: 'child',
      emailVerified: true,
      accountStatus: 'active',
      password: await require('../../shared/utils/passwordUtils').hashPassword(VALID_TEST_PASSWORD)
    });
    await seedTestUser(childUser);

    // Generate token for the child user
    const token = generateToken({
      userID: childUser.userID,
      email: childUser.email,
      accountType: 'child'
    });

    // Create mock event with authorization header
    const event = createMockEvent({}, '/auth/promote-account');
    event.headers = {
      ...event.headers,
      Authorization: `Bearer ${token}`
    };

    // Call the lambda
    const response = await promoteAccountLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    
    // Verify response
    expectSuccessResponse(response, 200, {
      message: expect.stringContaining('successfully promoted'),
      userID: childUser.userID,
      email: childUser.email
    });

    // Verify database update
    const updatedUser = await getTestUser(childUser.userID);
    expect(updatedUser).toBeTruthy();
    expect(updatedUser?.accountType).toBe('parent');
    expect(updatedUser?.parentAccount).toBe('ROOT');
    expect(updatedUser?.jwtSecret).toBeTruthy();
    expect(updatedUser?.accountBalance).toBe(3.00);
    expect(updatedUser?.availableBalance).toBe(3.00);
  });

  it('should return 401 when no authorization header is provided', async () => {
    const event = createMockEvent({}, '/auth/promote-account');
    
    const response = await promoteAccountLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    
    expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Authorization header is required');
  });

  it('should return 400 when account is already a parent', async () => {
    // Create a test parent user
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      accountType: 'parent',
      parentAccount: 'ROOT',
      emailVerified: true,
      accountStatus: 'active',
      password: await require('../../shared/utils/passwordUtils').hashPassword(VALID_TEST_PASSWORD)
    });
    await seedTestUser(parentUser);

    // Generate token for the parent user
    const token = generateToken({
      userID: parentUser.userID,
      email: parentUser.email,
      accountType: 'parent'
    });

    // Create mock event with authorization header
    const event = createMockEvent({}, '/auth/promote-account');
    event.headers = {
      ...event.headers,
      Authorization: `Bearer ${token}`
    };

    // Call the lambda
    const response = await promoteAccountLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    
    // Verify response
    expectErrorResponse(response, 400, 'INVALID_ACCOUNT_TYPE', 'Only child accounts can be promoted');
  });

  it('should handle staging environment correctly', async () => {
    // Create a test child user
    const childUser = await createTestUser({
      username: 'stagingchild',
      email: '<EMAIL>',
      accountType: 'child',
      parentAccount: 'APK_authiqa_parent_key',
      emailVerified: true,
      accountStatus: 'active',
      password: await require('../../shared/utils/passwordUtils').hashPassword(VALID_TEST_PASSWORD)
    });
    await seedTestUser(childUser);

    // Generate token for the child user
    const token = generateToken({
      userID: childUser.userID,
      email: childUser.email,
      accountType: 'child'
    });

    // Create mock event with staging host header
    const event = createMockEvent({}, '/auth/promote-account');
    event.headers = {
      ...event.headers,
      Authorization: `Bearer ${token}`,
      Host: 'staging.api.authiqa.com'
    };

    // Call the lambda
    const response = await promoteAccountLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    
    // Verify response
    expectSuccessResponse(response, 200, {
      message: expect.stringContaining('successfully promoted'),
      userID: childUser.userID,
      email: childUser.email
    });
    
    // Verify database update
    const updatedUser = await getTestUser(childUser.userID);
    expect(updatedUser).toBeTruthy();
    expect(updatedUser?.accountType).toBe('parent');
    expect(updatedUser?.parentAccount).toBe('ROOT');
  });

  it('should return 404 when user does not exist', async () => {
    // Generate token with non-existent user ID
    const token = generateToken({
      userID: 'USR_nonexistent',
      email: '<EMAIL>',
      accountType: 'child'
    });

    const event = createMockEvent({}, '/auth/promote-account');
    event.headers = {
      ...event.headers,
      Authorization: `Bearer ${token}`
    };

    const response = await promoteAccountLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    
    expectErrorResponse(response, 404, 'USER_NOT_FOUND', 'User not found');
  });

  it('should return 401 when token is invalid', async () => {
    const event = createMockEvent({}, '/auth/promote-account');
    event.headers = {
      ...event.headers,
      Authorization: 'Bearer invalid-token'
    };

    const response = await promoteAccountLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    
    expectErrorResponse(response, 401, 'INVALID_TOKEN', expect.any(String));
  });

  it('should return 403 when child account is not under Authiqa parent', async () => {
    // Create a test child user with a different parent
    const childUser = await createTestUser({
      username: 'otherchild',
      email: '<EMAIL>',
      accountType: 'child',
      parentAccount: 'APK_some_other_parent',
      emailVerified: true,
      accountStatus: 'active',
      password: await require('../../shared/utils/passwordUtils').hashPassword(VALID_TEST_PASSWORD)
    });
    await seedTestUser(childUser);

    // Generate token for the child user
    const token = generateToken({
      userID: childUser.userID,
      email: childUser.email,
      accountType: 'child'
    });

    const event = createMockEvent({}, '/auth/promote-account');
    event.headers = {
      ...event.headers,
      Authorization: `Bearer ${token}`
    };

    const response = await promoteAccountLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    
    expectErrorResponse(response, 403, 'NOT_ELIGIBLE', 'This account is not eligible for promotion');
  });
});

