import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorTypes } from '../shared/errorTypes';
import { SuccessResponse } from '../shared/responseUtils';
import { getUserByPublicKey, getUserByEmail, incrementOrganizationDetailsRetrievalCount } from '../shared/database/userOperations';
import { addCorsHeaders } from '../shared/corsHandler';
import { verifyToken } from '../shared/utils/tokenUtils';
import { getConfig } from '../shared/services/configService';
 
export const getOrganizationDetailsLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    // Load configuration from the service
    const config = await getConfig(event);
    
    console.log('[GET-ORGANIZATION-DETAILS] Lambda invoked with headers:', {
        host: event.headers.Host || event.headers.host,
        origin: event.headers.Origin || event.headers.origin
    });
    
    console.log('[CONFIG] GetOrganizationDetailsLambda using configuration service', {
        environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
        userTable: config.USER_DETAILS_TABLE_NAME
    });

    try {
        // Enhanced request logging
        console.log("Organization Details Lambda is triggered");
     
        let user = null;

        // Check for JWT authentication first
        const authHeader = event.headers.Authorization || event.headers.authorization;
        if (authHeader) {
            const token = authHeader.replace('Bearer ', '');
            try {
                const decodedToken = await verifyToken(token);
                if (decodedToken && decodedToken.email) {
                    user = await getUserByEmail(decodedToken.email, config.USER_DETAILS_TABLE_NAME);
                }
            } catch (error) {
                console.log('JWT verification failed:', error);
            }
        }

        // If user not found via JWT, try public key (existing flow)
        if (!user) {
            const publicKey = event.headers['x-public-key'] || event.headers['X-Public-Key'];
            if (!publicKey) {
                console.log('No authentication provided: Missing public Key and invalid/missing JWT');
                return addCorsHeaders(ErrorTypes.MISSING_PARENT_PUBLIC_KEY(), event);
            }

            // Log public key info
            console.log('public Key authentication attempt:', {
                present: true,
                length: publicKey.length,
                prefix: publicKey.substring(0, 6)
            });

            user = await getUserByPublicKey(publicKey, config.USER_DETAILS_TABLE_NAME);
            console.log('User Lookup Result via public Key:', {
                found: !!user
            });
        }

        if (!user) {
            return addCorsHeaders(ErrorTypes.USER_NOT_FOUND(), event);
        }

        console.log('User Lookup Result:', {
            found: true,
            accountType: user.accountType,
            hasAuthUrls: !!user.authUrls,
            parentAccount: user.parentAccount,
            domainRestrictionEnabled: user.domainRestrictionEnabled !== undefined ? user.domainRestrictionEnabled : true
        });

        // Try to increment counter but don't fail if it errors
        try {
            await incrementOrganizationDetailsRetrievalCount(user.userID, config.USER_DETAILS_TABLE_NAME);
        } catch (counterError) {
            console.warn('Failed to increment counter:', counterError);
            // Continue execution even if counter fails
        }

        // Log success response
        console.log('Returning organization details for user');
        
        // Return the response with credentials included
        // Default domainRestrictionEnabled to true if not set
        const domainRestrictionEnabled = user.domainRestrictionEnabled !== undefined ? user.domainRestrictionEnabled : true;
        
        return addCorsHeaders(SuccessResponse(200, {
            message: 'Organization details retrieved successfully',
            organizationName: user.organizationName,
            organizationUrl: user.organizationUrl,
            authUrls: user.authUrls,
            domainRestrictionEnabled: domainRestrictionEnabled,
            credentials: {
                publicKey: user.publicKey,
                jwtSecret: user.jwtSecret
            }
        }), event);
    } catch (error) {
        console.error('Error in getOrganizationDetails:', error);
        return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(), event);
    }
};

 
