/**
 * This file, balanceService.ts, is part of a payment processing system. 
 * It contains functions related to handling user payments, including creating 
 * payment records, updating payment statuses, and sending confirmation emails.
 * 
 * The main function in this file is `processPayment`, which takes a user object, 
 * the payment amount, and a transaction ID as parameters. It performs the following 
 * operations:
 * 
 * 1. Validates the user's balance to ensure it is defined.
 * 2. Logs the payment processing action for tracking purposes.
 * 3. Creates a payment record in a pending state using the `createPaymentRecord` 
 *    function, which records the payment details in the database.
 * 4. Attempts to update the payment status to 'COMPLETED'. If this fails, it logs 
 *    the error and marks the payment as 'FAILED'.
 * 
 * Overall, this file is crucial for managing the payment lifecycle, ensuring that 
 * payments are recorded accurately and that users are notified of their payment 
 * status.
 */

import { PaymentHistory, PaymentStatus, PaymentConfirmationEmailData } from '../types/payment';
import { createPaymentRecord, updatePaymentStatus } from '../database/paymentOperations';
import { sendPaymentConfirmationEmail } from '../emailService';
import Stripe from 'stripe';
// At the top with other imports
import { getPaymentById } from '../database/paymentOperations';
import { getUserById, updateUserBalance } from '../database/userOperations';
import { getStripeSecrets } from '../secrets';
import { InvoiceData } from '../types/invoice';
import { InvoiceService } from '../services/invoiceService';

// Add a singleton pattern for Stripe instance
let stripeInstance: Stripe | null = null;

async function getStripeInstance(): Promise<Stripe> {
  if (stripeInstance) {
    return stripeInstance;
  }

  const secrets = await getStripeSecrets();
  stripeInstance = new Stripe(secrets.secretKey, {
    apiVersion: '2025-02-24.acacia',
  });

  return stripeInstance;
}

export interface PaymentUser {
  userID: string;
  publicKey: string;
  email: string;
  username: string;
  balance: number;
  accountType: string;
}


export function validatePaymentRequest(
  user: PaymentUser,
  amount: number
): void {
  if (!user || !user.userID || !user.publicKey) {
    throw new Error('Invalid user data');
  }

  if (!amount || amount <= 0) {
    throw new Error('Invalid payment amount');
  }

  if (user.accountType !== 'parent') {
    throw new Error('Only parent accounts can make payments');
  }
}

interface PaymentInitializationResult {
  clientSecret: string;
  paymentId: string;
}

/**
 * Initializes a payment by creating a pending record and Stripe payment intent
 */
export async function initializePayment(
  user: PaymentUser,
  amount: number,
  invoiceEmail?: string,
  paymentHistoryTableName?: string,
  userTableName?: string // Add parameter for user table name
): Promise<PaymentInitializationResult> {
  try {
    validatePaymentRequest(user, amount);
    
    console.log('Initializing payment for user:', user.username, 'Amount:', amount, 'Invoice Email:', invoiceEmail || user.email);

    // Pass the user table name parameter
    const currentUser = await getUserById(user.userID, userTableName);
    if (!currentUser || currentUser.availableBalance === undefined) {
      throw new Error('Could not retrieve current user balance');
    }

    const payment = await createPaymentRecord(
      user.userID,
      user.publicKey,
      amount,
      currentUser.availableBalance,
      `init_${Date.now()}`,
      undefined,
      invoiceEmail,
      paymentHistoryTableName // Pass the table name to createPaymentRecord
    );
    
    // Get secure stripe instance
    const stripe = await getStripeInstance();
    
    // Log stripe secrets (unmasked)
    const secrets = await getStripeSecrets();
    console.log('Stripe secrets being used:', {
      publishableKey: secrets.publishableKey,
      secretKey: secrets.secretKey,
      webhookSecret: secrets.webhookSecret
    });

    // Add after stripe instance creation
    console.log('Creating Stripe payment intent for amount:', amount);
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert dollars to cents here
      currency: 'usd',
      metadata: { 
        paymentId: payment.paymentId,
        userId: user.userID 
      }
    });
    console.log('Stripe payment intent created:', paymentIntent.id);
    console.log('Here are the client secrets:', paymentIntent.client_secret);

    // Update the payment record with the stripe payment intent ID
    console.log('updating payment record with stripe payment intent ID after creation:', paymentIntent.id);
    await updatePaymentStatus(
      payment.paymentId,
      PaymentStatus.PENDING,
      undefined,
      paymentIntent.id,
      paymentHistoryTableName  // Add the stripe payment intent ID
    );

    // Add detailed logging of the payment record
    const updatedPayment = await getPaymentById(payment.paymentId,paymentHistoryTableName);
    console.log('Payment record after stripe intent update:', {
      paymentId: updatedPayment?.paymentId,
      userID: updatedPayment?.userID,
      publicKey: updatedPayment?.publicKey,
      amount: updatedPayment?.amount,
      status: updatedPayment?.status,
      createdAt: updatedPayment?.createdAt,
      completedAt: updatedPayment?.completedAt,
      previousBalance: updatedPayment?.previousBalance,
      newBalance: updatedPayment?.newBalance,
      transactionId: updatedPayment?.transactionId,
      stripePaymentIntentId: updatedPayment?.stripePaymentIntentId
    });

    return {
      clientSecret: paymentIntent.client_secret!,
      paymentId: payment.paymentId
    };
  } catch (error) {
    console.error('Error initializing payment:', error);
    throw new Error(`Payment initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Finalizes a payment after Stripe confirmation
 */
export async function finalizePayment(
  paymentId: string, 
  stripePaymentIntentId: string,
  paymentTableName: string,
  userTableName: string
): Promise<PaymentHistory> {
  try {
    // Get secure stripe instance
    const stripe = await getStripeInstance();
    
    // Retrieve the payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(stripePaymentIntentId);
    if (!paymentIntent) {
      throw new Error('Payment intent not found');
    }

    // Get the payment record from our database
    const payment = await getPaymentById(paymentId, paymentTableName);
    if (!payment) {
      throw new Error('Payment record not found');
    }

    if (payment.status === PaymentStatus.COMPLETED) {
      console.log(`Balance Service: Payment ${paymentId} is already completed. Skipping finalizePayment.`);
      return payment;
    }

    // Get user details
    const user = await getUserById(payment.userID, userTableName);
    if (!user) {
      throw new Error('User not found');
    }

    if (!user.organizationName) {
      throw new Error('Organization name is required');
    }

    if (user.availableBalance === undefined) {
      throw new Error('User available balance is undefined');
    }
    // Calculate new balance
    const newBalance = user.availableBalance + payment.amount;

    // Update payment status and user balance
    const updatedPayment = await updatePaymentStatus(
      paymentId,
      PaymentStatus.COMPLETED,
      newBalance,
      stripePaymentIntentId,
      paymentTableName
    );

    // Update user's balance in userAuthentication table
    await updateUserBalance(payment.userID, newBalance,userTableName);

    // We should use user.organizationName instead of username
    const invoiceData: InvoiceData = {
      invoiceNumber: await InvoiceService.generateInvoiceNumber(stripePaymentIntentId),
      organizationName: user.organizationName, // Now TypeScript knows it's not undefined
      amount: payment.amount,
      date: new Date().toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }),
      transactionId: stripePaymentIntentId
    };

    let invoicePdf: Buffer | undefined;
    try {
      invoicePdf = await InvoiceService.generatePDF(invoiceData);
    } catch (error) {
      console.error('Failed to generate invoice PDF:', error);
      // Still send email without PDF
    }

    // Send confirmation email
    await sendPaymentConfirmationEmail(
      payment.invoiceEmail || user.email, // Use invoiceEmail if available, otherwise fall back to user email
      {
        username: user.username,
        organizationName: user.organizationName,
        amount: payment.amount,
        newBalance: newBalance,
        transactionId: stripePaymentIntentId,
        paymentDate: new Date().toISOString(),
        invoicePdf: invoicePdf
      }
    );

    return updatedPayment;
  } catch (error) {
    console.error('Error finalizing payment:', error);
    // Mark payment as failed if something goes wrong
    await updatePaymentStatus(paymentId, PaymentStatus.FAILED, undefined, undefined, paymentTableName);
    throw new Error(`Payment finalization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Handles failed payment from Stripe webhook
 */
export async function handleFailedPayment(
  paymentId: string, 
  stripePaymentIntentId: string,
  paymentTableName: string,
  userTableName: string
): Promise<void> {
  try {
    // Get the payment record
    const payment = await getPaymentById(paymentId, paymentTableName);
    if (!payment) {
      throw new Error('Payment record not found');
    }

    // Get user details
    const user = await getUserById(payment.userID, userTableName);
    if (!user) {
      throw new Error('User not found');
    }

    // Add this check
    if (!user.organizationName) {
      throw new Error('Organization name is required');
    }

    // Update payment status to FAILED
    await updatePaymentStatus(
      paymentId,
      PaymentStatus.FAILED,
      undefined,
      stripePaymentIntentId,
      paymentTableName
    );

    // Send failure notification email
    await sendPaymentConfirmationEmail(
      payment.invoiceEmail || user.email, // Use invoiceEmail if available, otherwise fall back to user email
      {
        username: user.username,
        organizationName: user.organizationName, // Now TypeScript knows it's not undefined
        amount: payment.amount,
        newBalance: payment.previousBalance,
        transactionId: stripePaymentIntentId,
        paymentDate: new Date().toISOString()
      },
      'parent',
      'failed'
    );

  } catch (error) {
    console.error('Error handling failed payment:', error);
    throw error;
  }
}


