name: Deploy <PERSON> on AWS - Project (Authiqa)

on:
  push:
    branches:
      - main
      - staging
    paths:
      - "src/signUpLambda/**"
      - "src/signInLambda/**"
      - "src/emailConfirmationLambda/**"
      - "src/resendConfirmationEmailLambda/**"
      - "src/resetPasswordLambda/**"
      - "src/updatePasswordLambda/**"
      - "src/updateOrganizationLambda/**"
      - "src/getOrganizationDetailsLambda/**"
      - "src/getChildAccountsLambda/**"
      - "src/costCalculatorLambda/**"
      - "src/billingHistoryLambda/**"
      - "src/monthlyBillingStorageLambda/**"
      - "src/stripeWebhookLambda/**"
      - "src/paymentHistoryLambda/**"
      - "src/paymentInitializationLambda/**"
      - "src/shared/**"

env:
  REPO_NAME: "SignInSignUp"
  S3_BUCKET_PREFIX: "natuvea-lambda-deployments"
  STAGING_S3_BUCKET_PREFIX: "staging-natuvea-lambda-deployments"
  AWS_REGIONS: "eu-west-1"
  ENCRYPTION_KEY: ${{ secrets.ENCRYPTION_KEY }} 
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
          persist-credentials: true
          token: ${{ secrets.GH_TOKEN }}

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "20"
 
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGIONS }}

      - name: Install Dependencies
        run: npm i

      - name: Install TypeScript
        run: npm install typescript --save-dev

      - name: Install Yup
        run: npm install yup --save

      - name: Run Tests with Coverage
        run: npm run test:coverage

      - name: Change Permissions
        run: sudo chmod +x package_lambdas.sh

      - name: Run Package
        run: npm run package

      - name: Deploy Lambdas and Upload to S3
        run: |
          declare -A lambdas=(
            ["signUpLambda"]="signUpLambda"
            ["signInLambda"]="signInLambda"
            ["emailConfirmationLambda"]="emailConfirmationLambda"
            ["resendConfirmationEmailLambda"]="resendConfirmationEmailLambda"
            ["resetPasswordLambda"]="resetPasswordLambda"
            ["updatePasswordLambda"]="updatePasswordLambda"
            ["updateOrganizationLambda"]="updateOrganizationLambda"
            ["getOrganizationDetailsLambda"]="getOrganizationDetailsLambda"
            ["getChildAccountsLambda"]="getChildAccountsLambda"
            ["costCalculatorLambda"]="costCalculatorLambda"
            ["billingHistoryLambda"]="billingHistoryLambda"
            ["monthlyBillingStorageLambda"]="monthlyBillingStorageLambda"
            ["stripeWebhookLambda"]="stripeWebhookLambda"
            ["paymentHistoryLambda"]="paymentHistoryLambda"
            ["paymentInitializationLambda"]="paymentInitializationLambda"
          )

          CURRENT_BRANCH="${GITHUB_REF##*/}"
          echo "Current branch: $CURRENT_BRANCH"

          for region in $AWS_REGIONS; do
            # Choose the right S3 bucket
            if [[ "$CURRENT_BRANCH" == "staging" ]]; then
              S3_BUCKET="${STAGING_S3_BUCKET_PREFIX}"
              SUFFIX="-staging"
            else
              S3_BUCKET="${S3_BUCKET_PREFIX}"
              SUFFIX=""
            fi

            for lambda in "${!lambdas[@]}"; do
              BASE_NAME="${lambdas[$lambda]}"
              LAMBDA_NAME="${BASE_NAME}${SUFFIX}"
              ZIP_FILE="zips/${BASE_NAME}-package.zip"

              # Check if the zip file exists and is recent
              if [[ -f "$ZIP_FILE" && $(find "$ZIP_FILE" -mmin -3) ]]; then
                echo "Deploying $LAMBDA_NAME to $region."

                # Try to update Lambda
                if ! aws lambda update-function-code \
                  --function-name "$LAMBDA_NAME" \
                  --zip-file "fileb://$(pwd)/$ZIP_FILE" \
                  --region "$region"; then
                  echo "⚠️ Lambda function $LAMBDA_NAME not found. Uploading Zip to S3 instead."
                fi

                # Upload zip to S3
                echo "📤 Uploading $LAMBDA_NAME to bucket $S3_BUCKET..."
                aws s3 cp "$ZIP_FILE" \
                  "s3://${S3_BUCKET}/${LAMBDA_NAME}/${BASE_NAME}-package.zip" \
                  --region "$region"
              else
                echo "⏩ No recent package for $BASE_NAME. Skipping."
              fi
            done
          done

          
      - name: Generate Coverage Badges
        run: |
          mkdir -p badges
          npm run make:badges

      - name: Commit Coverage Badges
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add badges/
          git commit -m "chore: update coverage badges" || echo "No changes to commit"
          git push