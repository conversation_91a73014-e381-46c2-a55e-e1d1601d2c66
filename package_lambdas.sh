#!/bin/bash

# Define variables for the build directories, package names, and output directory
DIST_DIR="dist"  # The source build directory
SHARED_DIR="shared"
ZIP_OUTPUT_DIR="zips"
SRC_DIR="src"  # The target directory where we want to place handler.js

# Associative array of Lambda build directories and their zip output files
declare -A lambdas=(
    ["signUpLambda"]="build_signUpLambda"
    ["signInLambda"]="build_signInLambda"
    ["emailConfirmationLambda"]="build_emailConfirmationLambda"
    ["resendConfirmationEmailLambda"]="build_resendConfirmationEmailLambda"
    ["resetPasswordLambda"]="build_resetPasswordLambda"
    ["updatePasswordLambda"]="build_updatePasswordLambda"
    ["updateOrganizationLambda"]="build_updateOrganizationLambda"
    ["getOrganizationDetailsLambda"]="build_getOrganizationDetailsLambda"
    ["getChildAccountsLambda"]="build_getChildAccountsLambda"
    ["costCalculatorLambda"]="build_costCalculatorLambda"
    ["billingHistoryLambda"]="build_billingHistoryLambda"
    ["monthlyBillingStorageLambda"]="build_monthlyBillingStorageLambda"
    ["stripeWebhookLambda"]="build_stripeWebhookLambda"
    ["paymentHistoryLambda"]="build_paymentHistoryLambda"
    ["paymentInitializationLambda"]="build_paymentInitializationLambda"
    # Add more lambdas here as needed
)

# Check for changes in the shared directory
shared_changed=false
if ! git diff --quiet HEAD^ HEAD -- "src/$SHARED_DIR/**"; then
    shared_changed=true
    echo "Changes detected in the shared directory. Rebuilding all Lambdas."
fi

# Clean up any previous zip output directory (leave unchanged zips intact)
mkdir -p "$ZIP_OUTPUT_DIR"

# Iterate over each Lambda in the associative array
for lambda in "${!lambdas[@]}"; do
    LAMBDA_BUILD_DIR="${lambdas[$lambda]}"
    LAMBDA_ZIP="$ZIP_OUTPUT_DIR/${lambda}-package.zip"

    # Check for changes in the specific lambda directory or if shared directory changed
    if ! $shared_changed && git diff --quiet HEAD^ HEAD -- "src/$lambda/**"; then
        echo "No changes detected in $lambda. Skipping build and cleanup."
        continue
    fi
    echo "Changes detected in $lambda. building $lambda Lambda."
    echo "Building and packaging $lambda..."

    # Remove existing build directory and zip for the lambda
    rm -f "$LAMBDA_ZIP"
    rm -rf "$LAMBDA_BUILD_DIR"

    # Create build directory for the lambda
    mkdir -p "$LAMBDA_BUILD_DIR"
    mkdir -p "$LAMBDA_BUILD_DIR/$SRC_DIR/"

    # Copy lambda files from dist to the src directory
    if [ -d "$DIST_DIR/$lambda" ]; then
        cp -r "$DIST_DIR/$lambda/"* "$LAMBDA_BUILD_DIR/$SRC_DIR"
    else
        echo "Warning: $DIST_DIR/$lambda/ directory does not exist or is empty."
    fi

    # Copy shared files into the build directory
    cp -r "$DIST_DIR/$SHARED_DIR" "$LAMBDA_BUILD_DIR/"

    # Check if node_modules exist in the DIST_DIR
    if [ -d "node_modules" ]; then
        cp -r "node_modules" "$LAMBDA_BUILD_DIR/"
    else
        echo "Warning: node_modules/ directory does not exist."
    fi

    # Zip the contents of the build directory into the zips folder
    cd "$LAMBDA_BUILD_DIR"
    zip -r "../$LAMBDA_ZIP" .   # Create the zip file in the zips directory
    cd ..

    # Cleanup only for the lambda
    rm -rf "$LAMBDA_BUILD_DIR"
    echo "Packaging for $lambda complete."
done

# Output success message
echo "Packaging complete: zip files created in the '$ZIP_OUTPUT_DIR' directory."
