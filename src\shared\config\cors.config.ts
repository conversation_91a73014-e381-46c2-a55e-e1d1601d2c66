// This file handles cors for Express

import { CorsOptions } from 'cors';
import { Environment } from '../services/environment';

// Logging function for CORS requests
const logCorsRequest = (origin: string | undefined) => {
  console.log(`[CORS] Request from: ${origin || 'no origin'}`);
  if (Environment.isProduction()) {
    // Add your production logging here if needed
    // Example: logToMonitoring({ type: 'CORS_REQUEST', origin });
  }
};

export const corsOptions: CorsOptions = {
  origin: (origin, callback) => {
    // Log the request
    logCorsRequest(origin);
    
    // Allow the request but maintain security through public keys
    callback(null, true);
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Public-Key',
    'X-Amz-Date',
    'X-Amz-Security-Token',
    'Origin'
  ],
  credentials: true,
  maxAge: 86400,
  exposedHeaders: [
    'Access-Control-Allow-Origin',
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset'
  ]
};

export const getCorsHeaders = (origin?: string) => {
  const allowedOrigins = Array.isArray(corsOptions.origin) ? corsOptions.origin : [];
  const finalOrigin = origin && allowedOrigins.includes(origin) ? origin : allowedOrigins[0];
  
  const methods = Array.isArray(corsOptions.methods) 
    ? corsOptions.methods.join(',') 
    : typeof corsOptions.methods === 'string' 
      ? corsOptions.methods 
      : '';

  const headers = Array.isArray(corsOptions.allowedHeaders) 
    ? corsOptions.allowedHeaders.join(',') 
    : typeof corsOptions.allowedHeaders === 'string' 
      ? corsOptions.allowedHeaders 
      : '';

  const exposedHeaders = Array.isArray(corsOptions.exposedHeaders) 
    ? corsOptions.exposedHeaders.join(',') 
    : typeof corsOptions.exposedHeaders === 'string' 
      ? corsOptions.exposedHeaders 
      : '';
  
  return {
    'Access-Control-Allow-Origin': finalOrigin,
    'Access-Control-Allow-Methods': methods,
    'Access-Control-Allow-Headers': headers,
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': corsOptions.maxAge?.toString() || '86400',
    'Access-Control-Expose-Headers': exposedHeaders
  };
}; 