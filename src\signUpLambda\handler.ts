import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { validateSignUpInput } from '../shared/validation/userValidation';
import { Environment } from '../shared/services/environment';
import { addCorsHeaders } from '../shared/corsHandler';
import { UserService } from './services/userService';
import { ParentAccountService } from './services/parentAccountService';
import { EmailService } from './services/emailService';
import { NotificationService } from '../shared/services/notificationService';
import { getConfig } from '../shared/services/configService';

interface SignUpRequest {
  username: string;
  email: string;
  password: string;
  parentPublicKey?: string;
  verifyAuthPath?: string;
}

/**
 * Validates the request body and extracts the required fields
 */
function validateRequestBody(body: string | null): SignUpRequest {
  if (!body) {
    throw ErrorResponse(400, 'MISSING_REQUEST_BODY', 'Request body is required');
  }

  let requestBody;
  try {
    requestBody = JSON.parse(body);
  } catch {
    throw ErrorResponse(400, 'INVALID_REQUEST_BODY', 'Invalid JSON in request body');
  }

  const { username, email, password, parentPublicKey, verifyAuthPath } = requestBody;
  
  if (!email || !password) {
    throw ErrorResponse(400, 'MISSING_REQUIRED_FIELDS', 'Email and password are required');
  }

  return { username, email, password, parentPublicKey, verifyAuthPath };
}

/**
 * Validates the parent account if a parentPublicKey is provided
 */
async function validateParentAccount(parentPublicKey?: string, tableName?: string): Promise<void> {
  if (parentPublicKey) {
    console.log(`Validating parent account with key: ${parentPublicKey} using table: ${tableName || 'default'}`);
    const parentValidation = await ParentAccountService.validateParentPublicKey(parentPublicKey, tableName);
    if (!parentValidation.isValid) {
      throw parentValidation.error!;
    }
  }
}

/**
 * Prepares the success response data
 */
function prepareSuccessResponse(user: any, verificationToken: string): any {
  return {
    data: {
      userID: user.userID,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt,
      publicKey: user.publicKey,
      emailVerified: false,
      parentAccount: user.parentAccount,
      accountType: user.accountType,
      ...(Environment.isLocal() && {
        localDevelopment: {
          verificationCode: verificationToken
        }
      })
    }
  };
}

export const signUpLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  console.log('[SIGNUP] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });

  // Load configuration from the new service
  const config = await getConfig(event);
  
  console.log('[CONFIG] SignUpLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    frontendUrl: config.FRONTEND_URL,
    userTable: config.USER_DETAILS_TABLE_NAME,
    nodeEnv: config.NODE_ENV
  });

  
  try {
    // Input Validation
    const { username, email, password, parentPublicKey, verifyAuthPath } = validateRequestBody(event.body);
    
    console.log('SignUp Request received with paths:', {
      verifyAuthPath,
      email // logging email to track the flow
    });

    // Validate input
    const validationResult = await validateSignUpInput({ username, email, password, parentPublicKey });
    if (!validationResult.isValid && validationResult.error) {
      throw ErrorResponse(400, validationResult.error.code, validationResult.error.message);
    }

    // Parent Account Validation - Pass the table name from config
    await validateParentAccount(parentPublicKey, config.USER_DETAILS_TABLE_NAME);

    // User Creation - Pass the table name from config
    const { user, verificationToken } = await UserService.createUser({
      username,
      email,
      password,
      parentPublicKey
    }, config.USER_DETAILS_TABLE_NAME); // Pass table name here

    // Send Telegram notification (non-blocking)
    NotificationService.sendSignupNotification(
      username || 'unnamed', 
      email,
      event // Pass event to use configuration service
    ).catch(err => {
      console.error('Error sending signup notification:', err);
    });

    // Email Verification - pass the event to use configuration
    const emailResult = await EmailService.sendSignupVerification(
      email,
      verificationToken,
      parentPublicKey ? 'child' : 'parent',
      parentPublicKey,
      verifyAuthPath,
      event // Pass event to use configuration service
    );

    if (!emailResult.success) {
      throw emailResult.error!;
    }

    // Success Response - use config for determining local environment
    return addCorsHeaders(SuccessResponse(200, {
      data: {
        userID: user.userID,
        username: user.username,
        email: user.email,
        createdAt: user.createdAt,
        publicKey: user.publicKey,
        emailVerified: false,
        parentAccount: user.parentAccount,
        accountType: user.accountType,
        ...(config.NODE_ENV === 'local' && {
          localDevelopment: {
            verificationCode: verificationToken
          }
        })
      }
    }), event);

  } catch (error: unknown) {
    console.error('Error in signUpLambda:', error);
    
    if (error && 
        typeof error === 'object' && 
        'statusCode' in error && 
        'body' in error) {
      return addCorsHeaders(error as APIGatewayProxyResult, event);
    }
    
    // For unexpected errors, use config to determine if we should show detailed errors
    const errorMessage = config.NODE_ENV === 'local' 
      ? `${error instanceof Error ? error.message : 'An internal server error occurred'}` 
      : 'An internal server error occurred';
    
    return addCorsHeaders(ErrorResponse(500, 'INTERNAL_SERVER_ERROR', errorMessage), event);
  }
}; 
